"use client";

import { useState, useEffect, useRef } from 'react';
import { motion, useAnimation } from 'framer-motion';
import Background from '../components/Background';
import SpriteAnimator from '../components/SpriteAnimator';

/**
 * GameplayScreen - Main gameplay screen for Gig Hunt
 * Handles the hunting gameplay, <PERSON>'s behavior, and PixelPrey spawning
 */
const GameplayScreen = ({ onBackToMenu, isTransitioning }) => {
  const [williamState, setWilliamState] = useState('idle');
  const [williamFrame, setWilliamFrame] = useState(0);
  const [williamPosition, setWilliamPosition] = useState({ x: 0, y: 0 });
  const [isJumpingBehindGrass, setIsJumpingBehindGrass] = useState(false);

  const williamControls = useAnimation();
  const grassMaskRef = useRef(null);

  // William sprite configurations
  const williamSprites = {
    idle: {
      basePath: '/Projects/Games/gh/william/',
      frames: ['william_front_idle.svg'],
      frameRate: 1000 // Static frame
    },
    walking: {
      basePath: '/Projects/Games/gh/william/',
      frames: [
        'william_walk_right_01.svg',
        'william_walk_right_02.svg', 
        'william_walk_right_03.svg',
        'william_walk_right_04.svg'
      ],
      frameRate: 200
    },
    angry: {
      basePath: '/Projects/Games/gh/william/',
      frames: ['william_front_angry.svg'],
      frameRate: 1000
    },
    jumping: {
      basePath: '/Projects/Games/gh/william/',
      frames: [
        'william_jump_01.svg',
        'william_jump_02.svg'
      ],
      frameRate: 300
    }
  };

  // Test William animation cycle with movement and jumping
  useEffect(() => {
    const testSequence = async () => {
      // Reset position
      setWilliamPosition({ x: 0, y: 0 });
      setIsJumpingBehindGrass(false);

      // Wait a bit, then start walking
      await new Promise(resolve => setTimeout(resolve, 1000));
      setWilliamState('walking');

      // Walk across screen with movement animation
      williamControls.start({
        x: 200, // Move 200px to the right
        transition: { duration: 2, ease: "linear" }
      });

      // Walk for a bit
      await new Promise(resolve => setTimeout(resolve, 2000));
      setWilliamState('idle');

      // Idle, then get angry
      await new Promise(resolve => setTimeout(resolve, 1000));
      setWilliamState('angry');

      // Stay angry briefly, then jump
      await new Promise(resolve => setTimeout(resolve, 800));
      setWilliamState('jumping');

      // Jump sequence with physics-like motion
      const jumpSequence = async () => {
        // Jump up
        await williamControls.start({
          y: -120, // Jump up 120px (higher!)
          transition: { duration: 0.4, ease: "easeOut" }
        });

        // Check if we're in the second jump frame (behind grass)
        setTimeout(() => {
          setIsJumpingBehindGrass(true);
        }, 300); // Trigger mask when reaching peak

        // Fall down
        await williamControls.start({
          y: 0, // Back to ground
          transition: { duration: 0.6, ease: "easeIn" }
        });

        // Remove mask after landing
        setIsJumpingBehindGrass(false);
      };

      await jumpSequence();
      setWilliamState('idle');

      // Reset position after sequence
      setTimeout(() => {
        williamControls.start({
          x: 0,
          y: 0,
          transition: { duration: 1, ease: "easeInOut" }
        });
      }, 1000);
    };

    if (!isTransitioning) {
      testSequence();
    }
  }, [isTransitioning, williamControls]);

  const currentSprite = williamSprites[williamState];

  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* Background Layer with smooth slide transition */}
      <Background
        backgroundType="gameplay"
        isTransitioning={isTransitioning}
      />

      {/* Game Content Layer */}
      <div className="game-content-layer relative w-full h-full">
        {/* William Character */}
        <div className="absolute bottom-16 left-1/4 z-50">
          <motion.div
            initial={{ x: -100, opacity: 0 }}
            animate={{
              x: isTransitioning ? -100 : 0,
              opacity: isTransitioning ? 0 : 1
            }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <motion.div
              animate={williamControls}
              className={`relative ${isJumpingBehindGrass ? 'z-10' : 'z-50'}`}
            >
              <SpriteAnimator
                spriteBasePath={currentSprite.basePath}
                frameNames={currentSprite.frames}
                autoPlay={currentSprite.frames.length > 1}
                frameRate={currentSprite.frameRate}
                loop={true}
                className="w-24 h-24"
                alt={`William ${williamState}`}
              />
            </motion.div>
          </motion.div>
        </div>

        {/* Grass Mask Layer - appears when William jumps behind grass */}
        {isJumpingBehindGrass && (
          <motion.div
            ref={grassMaskRef}
            className="absolute bottom-0 left-0 w-full h-64 z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            style={{
              backgroundColor: '#419f00',
              pointerEvents: 'none'
            }}
          />
        )}
      </div>

      {/* Game HUD */}
      <div className="absolute top-4 left-4 z-60">
        <div
          className="text-white text-lg"
          style={{
            fontFamily: 'var(--font-retro)',
            textShadow: '2px 2px 0px #000'
          }}
        >
          <div>SCORE: 0</div>
          <div>KNOWLEDGE: 0</div>
        </div>
      </div>

      {/* Back to Menu Button (for testing) */}
      <div className="absolute top-4 right-4 z-60">
        <button
          onClick={onBackToMenu}
          className="px-4 py-2 bg-red-600 text-white border-2 border-red-400 hover:bg-red-700 transition-colors"
          style={{
            fontFamily: 'var(--font-retro)',
            fontSize: '16px'
          }}
        >
          BACK TO MENU
        </button>
      </div>

      {/* Debug Info */}
      <div className="absolute bottom-4 left-4 z-60">
        <div
          className="text-yellow-400 text-sm"
          style={{
            fontFamily: 'var(--font-retro)',
            textShadow: '1px 1px 0px #000'
          }}
        >
          <div>William State: {williamState.toUpperCase()}</div>
          <div>Behind Grass: {isJumpingBehindGrass ? 'YES' : 'NO'}</div>
          <div>Z-Index: {isJumpingBehindGrass ? '10' : '50'}</div>
        </div>
      </div>
    </div>
  );
};

export default GameplayScreen;
