"use client";

import { motion } from 'framer-motion';

/**
 * Background - Handles layered background elements for Gig Hunt
 * Manages the sliding transition from menu to gameplay background
 */
const Background = ({
  backgroundType = 'menu', // 'menu' or 'gameplay'
  isTransitioning = false,
  className = ""
}) => {
  
  const basePath = '/Projects/Games/gh/background/';
  
  // Use single background image for both menu and gameplay
  const backgroundImage = `${basePath}bg_image.svg`;

  return (
    <div className={`absolute inset-0 w-full h-full background-layer ${className}`}>
      {/* Single Background Image */}
      <motion.div
        className="relative w-full h-full"
        initial={{ x: backgroundType === 'gameplay' ? '100%' : '0%' }}
        animate={{
          x: isTransitioning ? (backgroundType === 'gameplay' ? '0%' : '-100%') : '0%'
        }}
        transition={{
          duration: 1.2,
          ease: [0.4, 0, 0.2, 1] // Custom easing for smooth slide
        }}
      >
        {/* Complete Background Image */}
        <div className="absolute inset-0 z-0">
          <img
            src={backgroundImage}
            alt="Game background"
            className="w-full h-full object-cover"
            style={{ imageRendering: 'pixelated' }}
          />
        </div>
      </motion.div>
    </div>
  );
};

export default Background;
